"use client";

import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

const EditableCell = React.forwardRef(({
  isEditing,
  value,
  onValueChange,
  onBlur,
  onKeyDown,
  className,
  children,
  ...props
}, ref) => {
  const [internalValue, setInternalValue] = useState(value || '');

  // Update internal value when external value changes
  useEffect(() => {
    setInternalValue(value || '');
  }, [value]);

  const handleInternalChange = (e) => {
    setInternalValue(e.target.value);
  };

  const handleBlur = (e) => {
    // Call the original onBlur handler
    if (onBlur) {
      onBlur(e);
    }
    // Call onValueChange with the current internal value
    if (onValueChange) {
      onValueChange(internalValue);
    }
  };

  if (isEditing) {
    return (
      <Input
        ref={ref}
        value={internalValue}
        onChange={handleInternalChange}
        onBlur={handleBlur}
        onKeyDown={onKeyDown}
        className={cn("w-full h-[38px]", className)}
        {...props}
      />
    );
  }

  return (
    <div className="w-full h-[38px] flex items-center">
      {children || value}
    </div>
  );
});

EditableCell.displayName = "EditableCell";

export { EditableCell };
