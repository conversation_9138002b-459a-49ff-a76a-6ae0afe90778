"use client";

import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

const EditableCell = React.forwardRef(({
  isEditing,
  value,
  onValueChange,
  onBlur,
  onKeyDown,
  className,
  children,
  ...props
}, ref) => {
  const [internalValue, setInternalValue] = useState(value || '');
  const [wasEditing, setWasEditing] = useState(false);

  // Update internal value when external value changes
  useEffect(() => {
    setInternalValue(value || '');
  }, [value]);

  // Handle transition from editing to not editing
  useEffect(() => {
    if (wasEditing && !isEditing) {
      // We just stopped editing, ensure the value is committed
      if (onValueChange && internalValue !== value) {
        onValueChange(internalValue);
      }
    }
    setWasEditing(isEditing);
  }, [isEditing, wasEditing, internalValue, value, onValueChange]);

  const handleInternalChange = (e) => {
    setInternalValue(e.target.value);
  };

  const handleBlur = (e) => {
    // Call onValueChange with the current internal value before blur
    if (onValueChange && internalValue !== value) {
      onValueChange(internalValue);
    }
    // Call the original onBlur handler
    if (onBlur) {
      onBlur(e);
    }
  };

  const handleKeyDown = (e) => {
    // For Tab and Enter, commit the value before the key event propagates
    if ((e.key === 'Tab' || e.key === 'Enter') && onValueChange && internalValue !== value) {
      onValueChange(internalValue);
    }
    // Call the original onKeyDown handler
    if (onKeyDown) {
      onKeyDown(e);
    }
  };

  if (isEditing) {
    return (
      <Input
        ref={ref}
        value={internalValue}
        onChange={handleInternalChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className={cn("w-full h-[38px]", className)}
        {...props}
      />
    );
  }

  return (
    <div className="w-full h-[38px] flex items-center">
      {children || value}
    </div>
  );
});

EditableCell.displayName = "EditableCell";

export { EditableCell };
