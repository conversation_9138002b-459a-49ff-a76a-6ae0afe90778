"use client";

import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

const EditableCell = React.forwardRef(({
  isEditing,
  value,
  columnKey,
  rowIndex,
  colIndex,
  onValueChange,
  onBlur,
  onNavigate,
  className,
  children,
  ...props
}, ref) => {
  const [internalValue, setInternalValue] = useState(value || '');
  const [wasEditing, setWasEditing] = useState(false);

  // Update internal value when external value changes
  useEffect(() => {setInternalValue(value || ''); }, [value]);

  // Handle transition from editing to not editing
  useEffect(() => {
    if (wasEditing && !isEditing) {
      // We just stopped editing, ensure the value is committed
      if (onValueChange && internalValue !== value) {
        onValueChange(internalValue);
      }
    }
    setWasEditing(isEditing);
  }, [isEditing, wasEditing, internalValue, value, onValueChange]);

  const handleInternalChange = (e) => {
    setInternalValue(e.target.value);
  };

  const handleBlur = (e) => {
    // Call onValueChange with the current internal value before blur
    if (onValueChange && internalValue !== value) {
      onValueChange(internalValue);
    }
    // Call the original onBlur handler
    if (onBlur) {
      onBlur(e);
    }
  };

  const performAsyncValidation = async (currentValue) => {
    if (columnKey === "nombre") {
      console.log("ANTES - value:", currentValue);

      // Simulate API validation call
      await ((ms) => new Promise((res) => setTimeout(res, ms)))(5000);

      console.log("DESPUES - value:", currentValue);
    }
  };

  const handleKeyDown = async (e) => {
    // Handle Tab key with validation completely within EditableCell
    if (e.key === 'Tab') {
      e.preventDefault();
      e.stopPropagation();

      // Commit the current value first
      if (onValueChange && internalValue !== value) {
        onValueChange(internalValue);
      }

      // Perform validation if needed
      await performAsyncValidation(internalValue);

      // Navigate after validation
      if (onNavigate) {
        onNavigate(rowIndex, colIndex, !e.shiftKey);
      }
      return;
    }

    // For Enter, commit the value and exit edit mode
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();

      if (onValueChange && internalValue !== value) {
        onValueChange(internalValue);
      }

      // Navigate to next cell
      if (onNavigate) {
        onNavigate(rowIndex, colIndex, true);
      }
      return;
    }

    // For Escape, cancel editing without saving
    if (e.key === 'Escape') {
      e.preventDefault();
      e.stopPropagation();

      // Reset to original value
      setInternalValue(value || '');

      // Exit edit mode by navigating to null
      if (onNavigate) {
        onNavigate(null, null, false);
      }
      return;
    }
  };

//console.log("Renderizando EditableCell")

  if (isEditing) {
    return (
      <Input
        ref={ref}
        value={internalValue}
        onChange={handleInternalChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className={cn("w-full h-[38px]", className)}
        {...props}
      />
    );
  }

  return (
    <div className="w-full h-[38px] flex items-center">
      {children || value}
    </div>
  );
});

EditableCell.displayName = "EditableCell";

export { EditableCell };
