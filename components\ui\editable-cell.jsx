"use client";

import React, { useState, useEffect } from 'react';
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

const EditableCell = React.forwardRef(({
  isEditing,
  value,
  columnKey,
  rowIndex,
  colIndex,
  onValueChange,
  onBlur,
  onNavigate,
  className,
  children,
  ...props
}, ref) => {
  const [internalValue, setInternalValue] = useState(value || '');
  const [wasEditing, setWasEditing] = useState(false);
  const [isValidating, setIsValidating] = useState(false);

  // Update internal value when external value changes
  useEffect(() => {setInternalValue(value || ''); }, [value]);

  // Handle transition from editing to not editing
  useEffect(() => {
    if (wasEditing && !isEditing) {
      // We just stopped editing, ensure the value is committed
      if (onValueChange && internalValue !== value) {
        onValueChange(internalValue);
      }
    }
    setWasEditing(isEditing);
  }, [isEditing, wasEditing, internalValue, value, onValueChange]);

  const handleInternalChange = (e) => {
    setInternalValue(e.target.value);
  };

  const handleBlur = (e) => {
    // Call onValueChange with the current internal value before blur
    if (onValueChange && internalValue !== value) {
      onValueChange(internalValue);
    }
    // Call the original onBlur handler
    if (onBlur) {
      onBlur(e);
    }
  };

  const performAsyncValidation = async (currentValue) => {
    if (columnKey === "nombre") {
      setIsValidating(true);
      console.log("ANTES - value:", currentValue);

      try {
        // Simulate API validation call
        await ((ms) => new Promise((res) => setTimeout(res, ms)))(5000);
        console.log("DESPUES - value:", currentValue);
      } finally {
        setIsValidating(false);
      }
    }
  };

  const handleKeyDown = async (e) => {
    // Handle Tab key with validation completely within EditableCell
    if (e.key === 'Tab') {
      // Prevent multiple TAB presses during validation
      if (isValidating) {
        e.preventDefault();
        e.stopPropagation();
        return;
      }

      e.preventDefault();
      e.stopPropagation();

    /*  // Commit the current value first
      if (onValueChange && internalValue !== value) {
        onValueChange(internalValue);
      } */

      // Perform validation if needed
      await performAsyncValidation(internalValue);

      // Navigate after validation
      if (onNavigate) {
        onNavigate(rowIndex, colIndex, !e.shiftKey);
      }
      return;
    }

    // For Enter, commit the value and exit edit mode
    if (e.key === 'Enter') {
      e.preventDefault();
      e.stopPropagation();

      if (onValueChange && internalValue !== value) {
        onValueChange(internalValue);
      }

      // Navigate to next cell
      if (onNavigate) {
        onNavigate(rowIndex, colIndex, true);
      }
      return;
    }

    // For Escape, cancel editing without saving
    if (e.key === 'Escape') {
      e.preventDefault();
      e.stopPropagation();

      // Reset to original value
      setInternalValue(value || '');

      // Exit edit mode by navigating to null
      if (onNavigate) {
        onNavigate(null, null, false);
      }
      return;
    }
  };

//console.log("Renderizando EditableCell")

  if (isEditing) {
    return (
      <div className="relative w-full">
        <Input
          ref={ref}
          value={internalValue}
          onChange={handleInternalChange}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          className={cn("w-full h-[38px]", isValidating && "opacity-75", className)}
          disabled={isValidating}
          {...props}
        />
        {isValidating && (
          <div className="absolute inset-0 flex items-center justify-end pr-2 pointer-events-none">
            <div className="text-xs text-gray-500">🔄 Validando...</div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="w-full h-[38px] flex items-center">
      {children || value}
    </div>
  );
});

EditableCell.displayName = "EditableCell";

export { EditableCell };
