import EditableTable from "@/components/EditableTable";
import { sampleColumnas, sampleDatos } from "@/lib/constants";

export default function Home() {
  return (
    <div className="container mx-auto py-12 px-4">
      <h1 className="text-3xl font-bold mb-8 text-center">
        Tabla Editable Inline
      </h1>
      
        
      
      <div className="rounded-lg border shadow-sm">
        <div className="p-6">
          <EditableTable 
            datos={sampleDatos} 
            columnas={sampleColumnas} 
          />
        </div>
      </div>
      
      <div className="mt-8 text-center text-muted-foreground">
        <p>
          Componente de tabla editable inline para NextJS 15 con TailwindCSS 4 y ShadCn
        </p>
      </div>
    </div>
  );
}