"use client";

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { EditableCell } from "@/components/ui/editable-cell";

const EditableTable = ({ datos = [], columnas = [] }) => {
  const [data, setData] = useState(datos);
  const [editCell, setEditCell] = useState(null);
  const inputRef = useRef(null);
  const [isValidating, setIsValidating] = useState(false);
  
  useEffect(() => {
    if (editCell && inputRef.current) {
      inputRef.current.focus();
    }
  }, [editCell]);

  const findNextEditableCell = (currentRow, currentCol, forward = true) => {
    let nextRow = currentRow;
    let nextCol = currentCol;

    const moveToNextPosition = () => {
      if (forward) {
        nextCol++;
        if (nextCol >= columnas.length) {
          nextCol = 0;
          nextRow = (nextRow + 1) % data.length;
        }
      } else {
        nextCol--;
        if (nextCol < 0) {
          nextCol = columnas.length - 1;
          nextRow = (nextRow - 1 + data.length) % data.length;
        }
      }
    };

    // First move to next position
    moveToNextPosition();
    
    // Keep looking until we find an editable cell or come back to start
    const startRow = currentRow;
    const startCol = currentCol;
    
    while (!(nextRow === startRow && nextCol === startCol)) {
      if (columnas[nextCol].editable) {
        return { row: nextRow, col: nextCol };
      }
      moveToNextPosition();
    }
    
    return null;
  };

  const performAsyncValidation = useCallback(async (columnKey, rowIndex, colIndex) => {
    if (columnKey === "nombre") {
      setIsValidating(true);
      console.log("ANTES - editCell:", editCell);

      // Use setTimeout instead of await to avoid blocking the event loop
      return new Promise((resolve) => {
        setTimeout(() => {
          console.log("DESPUES - editCell:", editCell);
          setIsValidating(false);

          // Proceed with navigation after validation
          const nextCell = findNextEditableCell(rowIndex, colIndex, true);
          if (nextCell) {
            setEditCell({
              rowIndex: nextCell.row,
              colKey: columnas[nextCell.col].key
            });
          }
          resolve();
        }, 5000);
      });
    }
  }, [editCell, columnas]);

  const handleKeyDown = (e, rowIndex, colIndex) => {
    if (e.key === 'Tab') {
      e.preventDefault();

      // Perform async validation without blocking
      if (columnas[colIndex].key === "nombre") {
        performAsyncValidation(columnas[colIndex].key, rowIndex, colIndex);
      } else {
        // Normal navigation without validation
        const nextCell = findNextEditableCell(rowIndex, colIndex, !e.shiftKey);
        if (nextCell) {
          setEditCell({
            rowIndex: nextCell.row,
            colKey: columnas[nextCell.col].key
          });
        }
      }
    } else if (e.key === 'Enter') {
      const colDef = columnas[colIndex];
      if (colDef.editable) {
        if (editCell) {
          setEditCell(null);
          // Move to next editable cell after pressing Enter
          const nextCell = findNextEditableCell(rowIndex, colIndex, true);
          if (nextCell) {
            setEditCell({
              rowIndex: nextCell.row,
              colKey: columnas[nextCell.col].key
            });
          }
        } else {
          setEditCell({
            rowIndex,
            colKey: colDef.key
          });
        }
      }
    } else if (e.key === 'Escape') {
      setEditCell(null);
    }
  };

  const handleCellValueChange = (rowIndex, colKey, value) => {
    const newData = [...data];
    newData[rowIndex] = {
      ...newData[rowIndex],
      [colKey]: value
    };
    setData(newData);
  };

  const handleCellBlur = (e) => {
    // Prevent blur when tabbing between cells
    if (!e.relatedTarget || !e.relatedTarget.closest('.editable-table')) {
      setEditCell(null);
    }
  };

  const handleCellClick = (rowIndex, colKey, editable) => {
    if (editable) {
      setEditCell({
        rowIndex,
        colKey
      });
    }
  };
  
  console.log("Renderizando EditableTable", {
    editCell,
    dataLength: data.length,
    isValidating,
    timestamp: Date.now()
  });

  return (
    <div className="w-full editable-table">
      {isValidating && (
        <div className="mb-2 p-2 bg-yellow-100 border border-yellow-300 rounded text-sm">
          🔄 Validando datos...
        </div>
      )}
      <Table>
        <TableHeader>
          <TableRow>
            {columnas.map((column) => (
              <TableHead 
                key={column.key}
                className="w-[200px] min-w-[200px]"
              >
                {column.header || column.key}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((row, rowIndex) => (
            <TableRow key={rowIndex} className="h-[56px]">
              {columnas.map((column, colIndex) => {
                const isEditing = editCell && 
                  editCell.rowIndex === rowIndex && 
                  editCell.colKey === column.key;
                
                return (
                  <TableCell
                    key={column.key}
                    className={`p-2 w-[200px] min-w-[200px] h-[56px] ${column.editable ? 'cursor-pointer' : ''}`}
                    tabIndex={column.editable ? 0 : -1}
                    onClick={() => handleCellClick(rowIndex, column.key, column.editable)}
                    onKeyDown={!isEditing ? (e) => handleKeyDown(e, rowIndex, colIndex) : undefined}
                  >
                    <EditableCell
                      ref={inputRef}
                      isEditing={isEditing}
                      value={row[column.key]}
                      onValueChange={(value) => handleCellValueChange(rowIndex, column.key, value)}
                      onBlur={handleCellBlur}
                      onKeyDown={isEditing ? (e) => {
                        if (e.key === 'Tab' || e.key === 'Enter' || e.key === 'Escape') {
                          e.stopPropagation();
                          handleKeyDown(e, rowIndex, colIndex);
                        }
                      } : undefined}
                    />
                  </TableCell>
                );
              })}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default EditableTable;